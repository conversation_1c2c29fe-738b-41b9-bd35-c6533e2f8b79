@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局响应式样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 响应式容器 */
.responsive-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

@media (min-width: 768px) {
  .responsive-container {
    padding: 0 24px;
  }
}

/* 响应式卡片间距 */
.responsive-card-spacing {
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .responsive-card-spacing {
    margin-bottom: 24px;
  }
}

/* 响应式表格容器 */
.responsive-table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.responsive-table-container .ant-table {
  min-width: 800px;
}

/* 移动端隐藏/显示 */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }

  .desktop-only {
    display: block;
  }
}

/* 响应式文字大小 */
.responsive-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

@media (min-width: 768px) {
  .responsive-title {
    font-size: 24px;
    margin: 0 0 16px 0;
  }
}

/* 响应式按钮组 */
.responsive-button-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

@media (min-width: 768px) {
  .responsive-button-group {
    flex-direction: row;
    gap: 12px;
  }
}

/* 修复Ant Design表格在移动端的显示 */
@media (max-width: 767px) {
  .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
  }

  .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }

  .ant-table-pagination {
    margin: 16px 0 0 0;
  }

  .ant-pagination-options {
    display: none;
  }
}

/* 响应式统计卡片 */
.responsive-statistic-card {
  text-align: center;
  padding: 16px;
}

@media (min-width: 768px) {
  .responsive-statistic-card {
    padding: 24px;
  }
}

/* 响应式搜索栏 */
.responsive-search-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .responsive-search-container {
    flex-direction: row;
    align-items: center;
    margin-bottom: 24px;
  }
}

/* 修复模态框在移动端的显示 */
@media (max-width: 767px) {
  .ant-modal {
    margin: 0;
    padding: 16px;
    max-width: none;
  }

  .ant-modal-content {
    border-radius: 8px;
  }

  .ant-modal-header {
    padding: 16px 16px 8px 16px;
  }

  .ant-modal-body {
    padding: 16px;
  }

  .ant-modal-footer {
    padding: 8px 16px 16px 16px;
  }
}

/* 导航栏响应式样式 */
.responsive-header {
  padding: 0 16px;
}

@media (min-width: 768px) {
  .responsive-header {
    padding: 0 24px;
  }
}

.responsive-logo {
  font-size: 16px;
}

@media (min-width: 768px) {
  .responsive-logo {
    font-size: 18px;
  }
}

.responsive-icon {
  font-size: 20px;
}

@media (min-width: 768px) {
  .responsive-icon {
    font-size: 24px;
  }
}

/* 移动端通知样式优化 */
@media (max-width: 767px) {
  .ant-notification {
    width: calc(100vw - 32px) !important;
    left: 16px !important;
    right: 16px !important;
    margin-top: 80px !important;
  }

  .ant-message {
    top: 80px !important;
  }

  .ant-message-notice {
    margin: 0 16px !important;
  }
}

/* 优化移动端表单样式 */
@media (max-width: 767px) {
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-label {
    padding-bottom: 4px;
  }

  .ant-input,
  .ant-select-selector,
  .ant-picker {
    height: 40px;
    font-size: 16px; /* 防止iOS缩放 */
  }

  .ant-btn {
    height: 40px;
    font-size: 14px;
  }

  .ant-btn-lg {
    height: 48px;
    font-size: 16px;
  }
}

/* 优化移动端卡片间距 */
@media (max-width: 767px) {
  .ant-card {
    margin-bottom: 12px;
  }

  .ant-card-body {
    padding: 16px;
  }

  .ant-card-head {
    padding: 0 16px;
    min-height: 48px;
  }

  .ant-card-head-title {
    font-size: 16px;
  }
}

/* 优化移动端统计卡片 */
@media (max-width: 767px) {
  .ant-statistic {
    text-align: center;
  }

  .ant-statistic-title {
    font-size: 12px;
    margin-bottom: 4px;
  }

  .ant-statistic-content {
    font-size: 20px;
  }
}

/* 优化移动端下拉菜单 */
@media (max-width: 767px) {
  .ant-dropdown {
    min-width: 200px;
  }

  .ant-dropdown-menu-item {
    padding: 12px 16px;
    font-size: 14px;
  }
}

/* 优化移动端抽屉 */
@media (max-width: 767px) {
  .ant-drawer-content-wrapper {
    width: 280px !important;
  }

  .ant-drawer-header {
    padding: 16px;
  }

  .ant-drawer-body {
    padding: 0;
  }
}

/* 无障碍访问优化 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 焦点可见性优化 */
.focus-visible:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* 高对比度模式支持 - 使用现代标准替代 -ms-high-contrast */
@media (prefers-contrast: high) {
  .ant-btn {
    border-width: 2px;
  }

  .ant-card {
    border-width: 2px;
  }
}

/* 强制颜色模式支持 - 替代已弃用的 -ms-high-contrast */
@media (forced-colors: active) {
  .ant-btn {
    border-width: 2px;
    forced-color-adjust: none;
  }

  .ant-card {
    border-width: 2px;
    forced-color-adjust: none;
  }

  .ant-table {
    forced-color-adjust: none;
  }

  .ant-modal {
    forced-color-adjust: none;
  }
}

/* 抑制 -ms-high-contrast 弃用警告 */
@supports (-ms-high-contrast: none) {
  /* 这个规则块为空，仅用于抑制弃用警告 */
  /* 实际的高对比度支持使用上面的 forced-colors 媒体查询 */
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #141414;
    --text-color: #ffffff;
    --border-color: #434343;
  }
}

/* 打印样式优化 */
@media print {
  .no-print {
    display: none !important;
  }

  .ant-btn,
  .ant-pagination,
  .ant-table-pagination {
    display: none !important;
  }

  .ant-table {
    font-size: 12px;
  }

  .ant-card {
    border: 1px solid #000;
    box-shadow: none;
  }
}

/* 加载状态优化 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 错误状态样式 */
.error-boundary {
  padding: 24px;
  text-align: center;
  border: 1px solid #ff4d4f;
  border-radius: 8px;
  background-color: #fff2f0;
}

/* 成功状态样式 */
.success-state {
  color: #52c41a;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 8px 12px;
}

/* 警告状态样式 */
.warning-state {
  color: #faad14;
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 4px;
  padding: 8px 12px;
}