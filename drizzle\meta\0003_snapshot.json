{"id": "a09191c4-14e9-4602-9a17-ef51b8577303", "prevId": "25d83046-a601-423e-91a8-432c19583a0b", "version": "7", "dialect": "postgresql", "tables": {"public.activation_codes": {"name": "activation_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "is_used": {"name": "is_used", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "used_at": {"name": "used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "used_by": {"name": "used_by", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "product_info": {"name": "product_info", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"activation_codes_code_unique": {"name": "activation_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.software": {"name": "software", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name_en": {"name": "name_en", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "description_en": {"name": "description_en", "type": "text", "primaryKey": false, "notNull": false}, "current_version": {"name": "current_version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "latest_version": {"name": "latest_version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "download_url": {"name": "download_url", "type": "text", "primaryKey": false, "notNull": false}, "download_url_backup": {"name": "download_url_backup", "type": "text", "primaryKey": false, "notNull": false}, "official_website": {"name": "official_website", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "system_requirements": {"name": "system_requirements", "type": "jsonb", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"software_name_unique": {"name": "software_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.software_announcements": {"name": "software_announcements", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "software_id": {"name": "software_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "title_en": {"name": "title_en", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "content_en": {"name": "content_en", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'general'"}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'normal'"}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "published_at": {"name": "published_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"software_announcements_software_id_software_id_fk": {"name": "software_announcements_software_id_software_id_fk", "tableFrom": "software_announcements", "tableTo": "software", "columnsFrom": ["software_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}